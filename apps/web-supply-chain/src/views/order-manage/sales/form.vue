<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { WorkflowStartInfo } from '@vben/types';

import type { OrderInfo } from '#/api';

import { computed, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import dayjs from 'dayjs';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addSalesListApi,
  BusinessStructureEnum,
  editSalesListApi,
  getOrderCodesApi,
  getProjectListApi,
  infoPurchaseListApi,
  infoSalesListApi,
} from '#/api';
import { ProjectSelector } from '#/components';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

import ProductInfo from './product-info.vue';

const emit = defineEmits(['register', 'ok']);
const { startWorkflow, WorkflowPreviewModal, initWorkflow } = useWorkflowBase();
const pageType = ref('edit');

const dictStore = useDictStore();
const colSpan = { md: 12, sm: 24 };

const orderInfoForm = ref<Partial<OrderInfo & WorkflowStartInfo>>({});
const formRef = ref();
const rules: Record<string, Rule[]> = {
  salesOrderName: [{ required: true, message: '请输入销售订单名称', trigger: 'blur' }],
  projectId: [{ required: true, message: '请选择所属项目名称', trigger: 'change' }],
  purchaseOrderId: [{ required: true, message: '请选择关联销售订单', trigger: 'change' }],
  purchaserCompanyCode: [{ required: true, message: '请选择下游企业', trigger: 'change' }],
  businessDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  estimatedEndDate: [{ required: true, message: '请选择预计结束日期', trigger: 'change' }],
  depositRatio: [{ required: true, message: '请输入保证金比例', trigger: 'blur' }],
  // depositAmount: [{ required: true, message: '请输入保证金金额', trigger: 'blur' }],
  paymentTermDays: [{ required: true, message: '请输入账期', trigger: 'blur' }],
  advanceRatio: [{ required: true, message: '请输入垫资比例', trigger: 'blur' }],
};
const title = computed(() => {
  if (pageType.value === 'audit') {
    return '审批销售订单';
  }
  if (orderInfoForm.value.id) {
    return '编辑销售订单';
  }
  return '新增销售订单';
});

const purchaserCompanyOptions = ref([]);

const productGridRef = ref();
const init = async (data: OrderInfo & { pageType: string }) => {
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_sales_order', businessKey: data.id });

  if (data.id) {
    orderInfoForm.value = await infoSalesListApi({ id: data.id });
    productGridRef.value.setProductData(orderInfoForm.value.salesOrderItemVOS);
    const projectDetailList = await getProjectListApi({ projectCode: orderInfoForm.value.projectCode });
    if (projectDetailList && projectDetailList.length > 0) {
      purchaserCompanyOptions.value = projectDetailList[0].projectPartners.filter(
        (item: any) => item.partnerType === 'PURCHASER',
      );
    }
  } else {
    orderInfoForm.value = data;
    orderInfoForm.value.taskType = '0';
  }
};
// 处理项目选择事件的函数
const handleProjectSelect = (_value: string, option: any) => {
  orderInfoForm.value.projectName = option.projectName;
  orderInfoForm.value.projectCode = option.projectCode; // 项目编号
  orderInfoForm.value.executorCompanyName = option.executorCompanyName; // 贸易执行企业名称
  orderInfoForm.value.executorCompanyCode = option.executorCompanyCode; // 贸易执行企业代码
  // PURCHASE,SALE
  orderInfoForm.value.businessStructure = option.businessStructure; // 业务结构
  orderInfoForm.value.projectModel = option.projectModel; // 项目模式 (建材模式, 产业模式等)
  orderInfoForm.value.businessManagerName = option.businessManager
    .map((v: { userName: string }) => v.userName)
    .join(',');
  orderInfoForm.value.operationManagerName = option.operationManager
    .map((v: { userName: string }) => v.userName)
    .join(',');
  purchaserCompanyOptions.value = option.projectPartners.filter((item: any) => item.partnerType === 'PURCHASER');
};
// 处理关联订单选择事件的函数
const handleCodesSelect = async (_value: string, option: any) => {
  orderInfoForm.value.purchaseOrderId = option.value;
  orderInfoForm.value.purchaseOrderName = option.label;
  orderInfoForm.value.purchaseOrderCode = option.orderCode;
  // 选择关联订单后，查询订单详情接口获取商品信息传给商品表格
  orderInfoFun(option.value);
};
const orderInfoFun = async (id: number) => {
  const itemRecord = await infoPurchaseListApi({ id });
  // 去掉id字段
  const processedData = itemRecord.purchaseOrderItemVOS
    ? itemRecord.purchaseOrderItemVOS.map(({ id: _id, ...rest }) => rest)
    : [];
  productGridRef.value.setProductData(processedData);
};

const save = async (type: string) => {
  await formRef.value.validate();
  const productRecord = await productGridRef.value?.getProductData();
  if (!productRecord) {
    return;
  }
  if (productRecord.items?.length === 0) {
    message.error('商品信息不能为空！');
    return;
  }
  orderInfoForm.value.salesOrderItemRequestS = productRecord.items;
  orderInfoForm.value.totalAmountWithTax = productRecord.totalAmountWithTax;
  orderInfoForm.value.totalTaxAmount = productRecord.totalTaxAmount;
  orderInfoForm.value.totalAmountWithoutTax = productRecord.totalAmountWithoutTax;
  orderInfoForm.value.status = type;

  if (type === 'SUBMITTED') {
    const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
    orderInfoForm.value.processDefinitionKey = processDefinitionKey;
    orderInfoForm.value.startUserSelectAssignees = startUserSelectAssignees;
  }

  changeOkLoading(true);
  let api = addSalesListApi;
  if (orderInfoForm.value.id) {
    api = editSalesListApi;
  }
  try {
    const res = await api(orderInfoForm.value as OrderInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const businessStructureType = computed(() => {
  return dictStore.formatter(orderInfoForm.value.businessStructure, 'BUS_STRUCTURE') as string;
});

const handleTotalAmountChange = (totals: any) => {
  orderInfoForm.value.totalAmountWithTax = totals.totalAmountWithTax;
  orderInfoForm.value.totalTaxAmount = totals.totalTaxAmount;
  orderInfoForm.value.totalAmountWithoutTax = totals.totalAmountWithoutTax;
};

const handleCompanySelect = (_value: string, option: { companyCode: string; companyName: string }) => {
  orderInfoForm.value.purchaserCompanyName = option.companyName;
};
const businessDate = computed({
  get() {
    return orderInfoForm.value.businessDate ? dayjs(orderInfoForm.value.businessDate).format('x') : undefined;
  },
  set(newValue: string) {
    orderInfoForm.value.businessDate = newValue ? Number(newValue) : undefined;
  },
});
const estimatedEndDate = computed({
  get() {
    return orderInfoForm.value.estimatedEndDate ? dayjs(orderInfoForm.value.estimatedEndDate).format('x') : undefined;
  },
  set(newValue: string) {
    orderInfoForm.value.estimatedEndDate = newValue ? Number(newValue) : undefined;
  },
});
const deliveryDeadline = computed({
  get() {
    return orderInfoForm.value.deliveryDeadline ? dayjs(orderInfoForm.value.deliveryDeadline).format('x') : undefined;
  },
  set(newValue: string) {
    orderInfoForm.value.deliveryDeadline = newValue ? Number(newValue) : undefined;
  },
});
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button v-if="pageType !== 'audit'" type="primary" @click="save('DRAFTING')">保存</a-button>
        <a-button v-if="pageType !== 'audit'" type="primary" @click="save('SUBMITTED')">提交</a-button>
      </a-space>
    </template>
    <a-form
      ref="formRef"
      :colon="false"
      :model="orderInfoForm"
      :rules="rules"
      :label-col="{ style: { width: '150px' } }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="订单编号" name="salesOrderCode">
            <a-input v-model:value="orderInfoForm.salesOrderCode" disabled v-if="orderInfoForm.id" />
            <a-input value="自动生成" disabled v-else />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="订单名称" name="salesOrderName">
            <a-input v-model:value="orderInfoForm.salesOrderName" :disabled="pageType === 'audit'" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="项目编号" name="projectCode">
            <a-input v-model:value="orderInfoForm.projectCode" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="项目名称" name="projectId">
            <ProjectSelector
              v-model="orderInfoForm.projectId"
              :disabled="orderInfoForm.id"
              @change="handleProjectSelect"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="业务结构" name="businessStructure">
            <a-input v-model:value="businessStructureType" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="项目模式" name="projectModel">
            <a-input :value="dictStore.formatter(orderInfoForm.projectModel, 'PROJECT_MODE')" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="orderInfoForm.businessStructure === BusinessStructureEnum.SALE">
          <a-form-item label="关联采购订单" name="purchaseOrderId">
            <ApiComponent
              v-model="orderInfoForm.purchaseOrderId"
              :component="Select"
              :api="getOrderCodesApi"
              :params="{ orderType: 'PURCHASE_ORDER', status: 'EFFECTIVE', projectId: orderInfoForm.projectId }"
              label-field="orderName"
              value-field="id"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              :disabled="orderInfoForm.id"
              @change="handleCodesSelect"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="orderInfoForm.businessStructure === BusinessStructureEnum.SALE">
          <a-form-item label="采购订单编号" name="purchaseOrderCode">
            <a-input v-model:value="orderInfoForm.purchaseOrderCode" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="贸易执行企业" name="executorCompanyName">
            <a-input v-model:value="orderInfoForm.executorCompanyName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="下游企业" name="purchaserCompanyCode">
            <a-select
              v-model:value="orderInfoForm.purchaserCompanyCode"
              :options="purchaserCompanyOptions"
              :disabled="pageType === 'audit'"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              :field-names="{ label: 'companyName', value: 'companyCode' }"
              @change="handleCompanySelect"
            />
          </a-form-item>
        </a-col>
        <!--<a-col v-bind="colSpan">-->
        <!--  <a-form-item label="业务负责人" name="businessManagerName">-->
        <!--    <a-input v-model:value="orderInfoForm.businessManagerName" disabled />-->
        <!--  </a-form-item>-->
        <!--</a-col>-->
        <!--<a-col v-bind="colSpan">-->
        <!--  <a-form-item label="运营负责人" name="operationManagerName">-->
        <!--    <a-input v-model:value="orderInfoForm.operationManagerName" disabled />-->
        <!--  </a-form-item>-->
        <!--</a-col>-->
        <a-col v-bind="colSpan">
          <a-form-item label="业务日期" name="businessDate">
            <a-date-picker
              v-model:value="businessDate"
              value-format="x"
              class="w-full"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="预计结束日期" name="estimatedEndDate">
            <a-date-picker
              v-model:value="estimatedEndDate"
              value-format="x"
              class="w-full"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="预付款金额" name="prepaymentAmount">
            <a-input-number
              v-model:value="orderInfoForm.prepaymentAmount"
              class="w-full"
              :controls="false"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="保证金金额" name="depositAmount">
            <a-input-number
              v-model:value="orderInfoForm.depositAmount"
              class="w-full"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <!--<a-col v-bind="colSpan" v-if="orderInfoForm.businessStructure === BusinessStructureEnum.PURCHASE">-->
        <!--  <a-form-item label="账期（天)" name="paymentTermDays">-->
        <!--    <a-input-number-->
        <!--      v-model:value="orderInfoForm.paymentTermDays"-->
        <!--      class="w-full"-->
        <!--      :disabled="pageType === 'audit'"-->
        <!--    />-->
        <!--  </a-form-item>-->
        <!--</a-col>-->
        <a-col v-bind="colSpan">
          <a-form-item label="截止交货日期" name="deliveryDeadline">
            <a-date-picker
              v-model:value="deliveryDeadline"
              value-format="x"
              class="w-full"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <!--<a-col v-bind="colSpan" v-if="orderInfoForm.businessStructure === BusinessStructureEnum.PURCHASE">-->
        <!--  <a-form-item label="垫资比例(%)" name="advanceRatio">-->
        <!--    <a-input-number-->
        <!--      v-model:value="orderInfoForm.advanceRatio"-->
        <!--      class="w-full"-->
        <!--      :max="999999"-->
        <!--      :disabled="pageType === 'audit'"-->
        <!--    />-->
        <!--  </a-form-item>-->
        <!--</a-col>-->
        <a-col v-bind="colSpan">
          <a-form-item label="任务类型" name="taskType">
            <a-select
              v-model:value="orderInfoForm.taskType"
              :options="dictStore.getDictList('TASK_TYPE')"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remarks">
            <a-textarea v-model:value="orderInfoForm.remarks" :rows="4" :disabled="pageType === 'audit'" />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 商品信息 -->
      <BasicCaption content="商品信息" />
      <ProductInfo
        ref="productGridRef"
        :form-props="{ ...orderInfoForm, businessStructureType }"
        @order-info-fun="orderInfoFun"
        @total-amount-change="handleTotalAmountChange"
        :page-type="pageType"
      />
      <BaseAttachmentList
        border="inner"
        :key="orderInfoForm.id"
        v-model="orderInfoForm.attachmentList"
        :business-id="orderInfoForm.id"
        business-type="SCM_SALES_ORDER"
        :edit-mode="pageType !== 'audit'"
      />
    </a-form>
    <WorkflowPreviewModal v-if="pageType === 'edit'" />
  </BasicPopup>
</template>
